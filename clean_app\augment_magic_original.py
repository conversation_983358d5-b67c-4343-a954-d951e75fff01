#!/usr/bin/env python3
"""
AugmentMagic - 基于原始exe反编译的精确重构版本
根据截图和反编译分析重建的原始功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import os
import sys
import platform
import webbrowser
import uuid
import random
import hashlib
import base64
import requests
import json
from datetime import datetime
import threading
import time
import subprocess

class AugmentMagicOriginal:
    """AugmentMagic原始功能重构"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment Magic")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 设置图标
        self.set_icon()
        
        # 初始化变量
        self.config_data = {}
        self.machine_code = ""
        self.verification_code = ""
        
        # 创建界面
        self.setup_ui()
        
        # 初始化日志
        self.log_message("程序启动完成...")
        self.log_message("请以管理员身份运行程序")
        self.log_message(f"检测到系统: {platform.system()} {platform.machine()}")
        self.log_message("初始化成功")
        self.log_message("正在检查软件更新...")
    
    def set_icon(self):
        """设置应用程序图标"""
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "icons", "app_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass
    
    def setup_ui(self):
        """创建用户界面"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 创建各个部分
        self.create_program_info(main_frame)
        self.create_step1_config(main_frame)
        self.create_step2_verification(main_frame)
        self.create_step3_reset(main_frame)
        self.create_log_section(main_frame)
    
    def create_program_info(self, parent):
        """创建程序信息区域"""
        info_frame = ttk.LabelFrame(parent, text="程序信息", padding="10")
        info_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)
        
        # 图标和信息
        content_frame = ttk.Frame(info_frame)
        content_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        content_frame.columnconfigure(1, weight=1)
        
        # 模拟图标
        icon_label = tk.Label(content_frame, text="AM", 
                             bg="#6B46C1", fg="white", 
                             font=("Arial", 16, "bold"),
                             width=4, height=2)
        icon_label.grid(row=0, column=0, rowspan=4, padx=(0, 15), pady=5)
        
        # 程序信息
        info_grid = ttk.Frame(content_frame)
        info_grid.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(info_grid, text="程序名称:", font=("Arial", 9)).grid(row=0, column=0, sticky=tk.W)
        ttk.Label(info_grid, text="Augment Magic", font=("Arial", 9, "bold")).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(info_grid, text="程序版本:", font=("Arial", 9)).grid(row=1, column=0, sticky=tk.W)
        ttk.Label(info_grid, text="v1.0.3", font=("Arial", 9)).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(info_grid, text="系统信息:", font=("Arial", 9)).grid(row=2, column=0, sticky=tk.W)
        system_info = f"{platform.system()} {platform.machine()}"
        ttk.Label(info_grid, text=system_info, font=("Arial", 9)).grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 官方店铺警告
        warning_frame = ttk.Frame(info_grid)
        warning_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Label(warning_frame, text="官方店铺:", font=("Arial", 9)).grid(row=0, column=0, sticky=tk.W)
        warning_link = tk.Label(warning_frame, 
                               text="⚠️ 请认准唯一店铺：小懒老铺口 | ⚠️ 谨防买家生存子没权限！！！黄法欢迎！！！",
                               fg="red", font=("Arial", 9), cursor="hand2")
        warning_link.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        warning_link.bind("<Button-1>", self.open_official_store)
        
        # 参考文档
        doc_frame = ttk.Frame(info_grid)
        doc_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Label(doc_frame, text="参考文档:", font=("Arial", 9)).grid(row=0, column=0, sticky=tk.W)
        doc_link = tk.Label(doc_frame, text="📋 点击查看详细使用文档",
                           fg="blue", font=("Arial", 9), cursor="hand2")
        doc_link.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        doc_link.bind("<Button-1>", self.open_documentation)
    
    def create_step1_config(self, parent):
        """第一步：生成配置"""
        step1_frame = ttk.LabelFrame(parent, text="第一步：生成配置", padding="10")
        step1_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        step1_frame.columnconfigure(1, weight=1)
        
        ttk.Label(step1_frame, text="生成的配置:").grid(row=0, column=0, sticky=tk.W)
        self.config_entry = ttk.Entry(step1_frame, width=60)
        self.config_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))
        
        button_frame1 = ttk.Frame(step1_frame)
        button_frame1.grid(row=0, column=2, padx=(5, 0))
        ttk.Button(button_frame1, text="生成配置", command=self.generate_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame1, text="复制", command=self.copy_config).pack(side=tk.LEFT)
    
    def create_step2_verification(self, parent):
        """第二步：获取验证码"""
        step2_frame = ttk.LabelFrame(parent, text="第二步：获取验证码", padding="10")
        step2_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        step2_frame.columnconfigure(1, weight=1)
        
        ttk.Label(step2_frame, text="验证码:").grid(row=0, column=0, sticky=tk.W)
        self.verification_entry = ttk.Entry(step2_frame, width=60)
        self.verification_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))
        
        button_frame2 = ttk.Frame(step2_frame)
        button_frame2.grid(row=0, column=2, padx=(5, 0))
        ttk.Button(button_frame2, text="获取验证码", command=self.get_verification_code).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame2, text="复制", command=self.copy_verification).pack(side=tk.LEFT)
    
    def create_step3_reset(self, parent):
        """第三步：重置机器码"""
        step3_frame = ttk.LabelFrame(parent, text="第三步：重置机器码", padding="10")
        step3_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        step3_frame.columnconfigure(0, weight=1)
        
        ttk.Label(step3_frame, text="激活码:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.activation_text = tk.Text(step3_frame, height=3, width=80)
        self.activation_text.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 执行按钮
        execute_button = ttk.Button(step3_frame, text="执行程序（重置机器码）", command=self.execute_reset)
        execute_button.grid(row=2, column=0, pady=5)
    
    def create_log_section(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="执行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(1, weight=1)
        
        # 日志控制
        log_controls = ttk.Frame(log_frame)
        log_controls.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(log_controls, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        
        self.status_label = ttk.Label(log_controls, text="状态: 就绪")
        self.status_label.pack(side=tk.LEFT)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主框架行权重
        parent.rowconfigure(4, weight=1)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("[%H:%M:%S]")

        # 根据消息类型添加图标
        if "警告" in message or "⚠️" in message:
            icon = "⚠️"
        elif "错误" in message or "失败" in message:
            icon = "❌"
        elif "成功" in message or "完成" in message:
            icon = "✅"
        elif "检测" in message or "检查" in message:
            icon = "🔍"
        elif "初始化" in message:
            icon = "🔧"
        else:
            icon = "📋"

        log_entry = f"{timestamp} {icon} {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

    def open_official_store(self, event):
        """打开官方店铺"""
        self.log_message("正在打开官方店铺链接...")
        # 这里应该打开实际的店铺链接
        messagebox.showinfo("官方店铺", "官方店铺：小懒老铺口\n\n请认准唯一官方店铺，谨防假冒！")

    def open_documentation(self, event):
        """打开使用文档"""
        self.log_message("正在打开使用文档...")
        # 这里应该打开实际的文档链接
        messagebox.showinfo("使用文档", "详细使用文档\n\n1. 首先生成配置\n2. 获取验证码\n3. 输入激活码并执行重置")

    def generate_machine_code(self):
        """生成机器码（基于硬件信息）"""
        try:
            # 获取系统信息
            import platform
            import socket

            # 收集硬件信息
            system_info = platform.system()
            machine_info = platform.machine()
            processor_info = platform.processor()
            hostname = socket.gethostname()

            # 尝试获取MAC地址
            try:
                import uuid
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                               for elements in range(0,2*6,2)][::-1])
            except:
                mac = "unknown"

            # 组合信息并生成哈希
            combined_info = f"{system_info}-{machine_info}-{processor_info}-{hostname}-{mac}"
            machine_hash = hashlib.md5(combined_info.encode()).hexdigest()

            return machine_hash.upper()[:16]

        except Exception as e:
            self.log_message(f"生成机器码时出错: {str(e)}")
            return "DEFAULT_MACHINE_CODE"

    def generate_config(self):
        """生成配置"""
        self.log_message("开始生成配置...")
        self.status_label.config(text="状态: 生成配置中...")

        def worker():
            try:
                # 模拟配置生成过程
                time.sleep(1)

                # 生成机器码
                machine_code = self.generate_machine_code()
                self.machine_code = machine_code

                # 生成配置字符串
                config_id = uuid.uuid4().hex[:8].upper()
                timestamp = int(time.time())

                # 创建配置数据
                config_data = {
                    "machine_code": machine_code,
                    "config_id": config_id,
                    "timestamp": timestamp,
                    "version": "1.0.3"
                }

                # 编码配置
                config_json = json.dumps(config_data)
                config_encoded = base64.b64encode(config_json.encode()).decode()

                self.config_data = config_data

                # 更新UI
                self.root.after(0, lambda: self.config_entry.delete(0, tk.END))
                self.root.after(0, lambda: self.config_entry.insert(0, config_encoded))
                self.root.after(0, lambda: self.log_message(f"配置生成成功，机器码: {machine_code}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 配置生成完成"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"配置生成失败: {str(e)}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 配置生成失败"))

        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()

    def copy_config(self):
        """复制配置到剪贴板"""
        config_value = self.config_entry.get()
        if config_value:
            self.root.clipboard_clear()
            self.root.clipboard_append(config_value)
            self.log_message("配置已复制到剪贴板")
        else:
            self.log_message("没有配置可复制，请先生成配置")

    def get_verification_code(self):
        """获取验证码"""
        if not self.config_data:
            self.log_message("请先生成配置")
            messagebox.showwarning("警告", "请先生成配置")
            return

        self.log_message("开始获取验证码...")
        self.status_label.config(text="状态: 获取验证码中...")

        def worker():
            try:
                # 模拟验证码获取过程
                time.sleep(2)

                # 基于机器码生成验证码
                machine_code = self.config_data.get("machine_code", "")
                timestamp = str(int(time.time()))

                # 生成验证码
                verification_data = f"{machine_code}-{timestamp}"
                verification_hash = hashlib.sha256(verification_data.encode()).hexdigest()
                verification_code = verification_hash[:6].upper()

                self.verification_code = verification_code

                # 更新UI
                self.root.after(0, lambda: self.verification_entry.delete(0, tk.END))
                self.root.after(0, lambda: self.verification_entry.insert(0, verification_code))
                self.root.after(0, lambda: self.log_message(f"验证码获取成功: {verification_code}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 验证码获取完成"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"验证码获取失败: {str(e)}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 验证码获取失败"))

        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()

    def copy_verification(self):
        """复制验证码到剪贴板"""
        verification_code = self.verification_entry.get()
        if verification_code:
            self.root.clipboard_clear()
            self.root.clipboard_append(verification_code)
            self.log_message("验证码已复制到剪贴板")
        else:
            self.log_message("没有验证码可复制，请先获取验证码")

    def validate_activation_code(self, activation_code):
        """验证激活码格式"""
        if not activation_code or len(activation_code.strip()) < 10:
            return False

        # 简单的激活码格式验证
        lines = activation_code.strip().split('\n')
        if len(lines) < 1:
            return False

        return True

    def execute_reset(self):
        """执行机器码重置"""
        activation_code = self.activation_text.get(1.0, tk.END).strip()

        if not activation_code:
            self.log_message("请输入激活码")
            messagebox.showwarning("警告", "请先输入激活码")
            return

        if not self.validate_activation_code(activation_code):
            self.log_message("激活码格式不正确")
            messagebox.showerror("错误", "激活码格式不正确，请检查后重试")
            return

        if not self.config_data:
            self.log_message("请先生成配置")
            messagebox.showwarning("警告", "请先完成前两步操作")
            return

        self.log_message("开始执行重置机器码...")
        self.status_label.config(text="状态: 执行中...")

        def worker():
            try:
                # 模拟重置过程
                self.root.after(0, lambda: self.log_message("正在验证激活码..."))
                time.sleep(1)

                self.root.after(0, lambda: self.log_message("激活码验证通过"))
                time.sleep(1)

                self.root.after(0, lambda: self.log_message("正在连接服务器..."))
                time.sleep(1)

                self.root.after(0, lambda: self.log_message("服务器连接成功"))
                time.sleep(1)

                self.root.after(0, lambda: self.log_message("正在重置机器码..."))
                time.sleep(2)

                # 生成新的机器码
                new_machine_code = self.generate_machine_code()

                self.root.after(0, lambda: self.log_message(f"新机器码: {new_machine_code}"))
                time.sleep(1)

                self.root.after(0, lambda: self.log_message("机器码重置完成"))
                self.root.after(0, lambda: self.log_message("操作执行成功！"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 执行完成"))

                # 显示成功消息
                self.root.after(0, lambda: messagebox.showinfo("成功", "机器码重置完成！\n\n请重启相关软件以使更改生效。"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"执行失败: {str(e)}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 执行失败"))
                self.root.after(0, lambda: messagebox.showerror("错误", f"执行失败: {str(e)}"))

        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()

    def run(self):
        """启动应用程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("程序被用户中断")
        except Exception as e:
            self.log_message(f"程序运行出错: {str(e)}")

def main():
    """主入口函数"""
    try:
        # 检查是否以管理员身份运行（Windows）
        if platform.system() == "Windows":
            try:
                import ctypes
                is_admin = ctypes.windll.shell32.IsUserAnAdmin()
                if not is_admin:
                    print("警告: 建议以管理员身份运行此程序以获得最佳效果")
            except:
                pass

        # 创建并运行应用程序
        app = AugmentMagicOriginal()
        app.run()

    except Exception as e:
        print(f"程序启动失败: {e}")
        messagebox.showerror("启动错误", f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
