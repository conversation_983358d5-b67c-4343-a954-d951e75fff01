@echo off
title Augment Magic - 原始功能版本
color 0A
echo.
echo ========================================
echo    Augment Magic - 原始功能重构版本
echo ========================================
echo.
echo 📋 基于原始exe文件反编译重构
echo 🎯 完全还原原始程序界面和功能
echo 🔧 支持配置生成、验证码获取、机器码重置
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 检测到管理员权限
) else (
    echo ⚠️  警告: 未检测到管理员权限
    echo    建议右键选择"以管理员身份运行"以获得最佳效果
)
echo.

REM 检查Python环境
echo [1/3] 检查Python环境...
d:\soft\miniconda\python.exe --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo    请确保Python已正确安装
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

REM 检查依赖模块
echo [2/3] 检查依赖模块...
d:\soft\miniconda\python.exe -c "import tkinter, hashlib, base64, json, uuid, platform, threading" >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 缺少必要的Python模块
    pause
    exit /b 1
)
echo ✅ 依赖模块检查通过

echo [3/3] 启动应用程序...
echo.
echo 🚀 正在启动 Augment Magic 原始功能版本...
echo.
echo 📝 功能说明:
echo    • 第一步: 生成配置 - 基于硬件信息生成唯一配置
echo    • 第二步: 获取验证码 - 生成安全验证码
echo    • 第三步: 重置机器码 - 执行机器码重置操作
echo.
echo 💡 使用提示:
echo    • 请按顺序完成三个步骤
echo    • 建议以管理员身份运行
echo    • 执行过程中请勿关闭程序
echo.

REM 启动应用程序
d:\soft\miniconda\python.exe augment_magic_original.py

echo.
echo 📱 Augment Magic 已关闭
echo 感谢使用！
pause
